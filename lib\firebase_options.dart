// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyAaEQOmP3aYTboDeWcSPwLygvohfC3bZLw',
    appId: '1:679736072320:web:62d909d5ba230991e72e65',
    messagingSenderId: '679736072320',
    projectId: 'edu-writer-ai',
    authDomain: 'edu-writer-ai.firebaseapp.com',
    storageBucket: 'edu-writer-ai.firebasestorage.app',
    measurementId: 'G-T75XP8WXKL',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyCckVfDJMY0ogJPgyAYrDLb1CSH7ueN4T4',
    appId: '1:679736072320:android:1926bc3622eb9b81e72e65',
    messagingSenderId: '679736072320',
    projectId: 'edu-writer-ai',
    storageBucket: 'edu-writer-ai.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBJAgXOLZQaFIdLmnOChjrrChWl26S1UD8',
    appId: '1:679736072320:ios:f342be895d51ea3ee72e65',
    messagingSenderId: '679736072320',
    projectId: 'edu-writer-ai',
    storageBucket: 'edu-writer-ai.firebasestorage.app',
    iosBundleId: 'com.example.eduWriterAi',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyBJAgXOLZQaFIdLmnOChjrrChWl26S1UD8',
    appId: '1:679736072320:ios:f342be895d51ea3ee72e65',
    messagingSenderId: '679736072320',
    projectId: 'edu-writer-ai',
    storageBucket: 'edu-writer-ai.firebasestorage.app',
    iosBundleId: 'com.example.eduWriterAi',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyAaEQOmP3aYTboDeWcSPwLygvohfC3bZLw',
    appId: '1:679736072320:web:d1032ba4782550f4e72e65',
    messagingSenderId: '679736072320',
    projectId: 'edu-writer-ai',
    authDomain: 'edu-writer-ai.firebaseapp.com',
    storageBucket: 'edu-writer-ai.firebasestorage.app',
    measurementId: 'G-30GRXSWKWN',
  );
}
